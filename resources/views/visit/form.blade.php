@include('blocks.sweetalt')
@include('blocks.validation')
@include('blocks.autocomplete')

@push('css_plugins')
    <!-- Auto Complete Live Search -->
    <link rel="stylesheet" type="text/css"
          href="{{ asset('plugins/bower_components/bootstrap-datepicker/bootstrap-datepicker.min.css') }}"/>
    <link rel="stylesheet" type="text/css" href="{{ asset('plugins/icheck/skins/all.css') }}"/>
    <link rel="stylesheet" type="text/css" href="{{ asset('css/store.openTimeModal.css') }}"/>
@endpush

@push('css')
    <style>
        .radio-cont,
        .check-cont {
            display: flex;
        }

        .radio-cont .radio-txt {
            flex: 1;
            align-self: center;
            text-align: center;
            font-size: 20px;
        }

        .radio-cont .radio-items {
            flex: 3;
            display: flex;
        }

        .radio-cont .radio-items .item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-left: 0;
            margin-top: 0;
            margin-bottom: 0px;
        }

        .radio-cont .radio-items .num {
            font-size: 14px;
            margin-bottom: 0px;
        }

        .radio-cont .radio-items .radio label::after,
        .radio-cont .radio-items .radio label::before {
            margin-left: -6px;
        }

        .check-cont .checkbox {
            flex: 1;
            text-align: center;
            padding-left: 0;
        }

        .checkbox + .checkbox {
            margin-top: 10px;
        }

        .datepicker.dropdown-menu {
            margin-top: 60px;
        }

        .tooltip-inner {
            text-align: left;
            max-width: 350px;
        }

        .upload-img {
            position: relative;
            display: inline-block;
            width: 100%;
            margin: 5px;
            padding: 10px;
            line-height: 1.42857143;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            -webkit-transition: all .2s ease-in-out;
            -o-transition: all .2s ease-in-out;
            transition: all .2s ease-in-out;
        }

        .upload-img img {
            display: block;
            margin: auto;
            width: 100%;
            height: 200px;
            overflow: auto;
        }

        .upload-img a {
            font-size: 30px;
            display: block;
            width: 30px;
            height: 30px;
            position: absolute;
            top: -10px;
            right: -10px;
            /* background: #c00; */
            overflow: hidden;
            border: 2px solid #ddd;
            border-radius: 45px;
            line-height: 24px;
            text-align: center;
            color: #b1b1b1;
            background: #ddd;
            z-index: 99;
        }
    </style>
@endpush

@extends('layouts.main')

@section('content')

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-info">
                <div class="panel-heading">
                    @if (!isset($id))
                        新增拜訪紀錄
                    @else
                        修改拜訪紀錄
                    @endif
                </div>
                <div class="panel-wrapper collapse in" aria-expanded="true">
                    <div class="panel-body">
                        <form id="form" action="/visit/form" method="POST" enctype="multipart/form-data">
                            @csrf
                            <input type="hidden" name="id"
                                   value="{{ isset($storeVisit['id']) ? $storeVisit['id'] : 0 }}">
                            <input type="hidden" name="store_id"
                                   value="{{ isset($storeVisit['store_id']) ? $storeVisit['store_id'] : 0 }}">
                            <div class="form-body">
                                <div class="row">
                                    <!--
                                                <div class="col-md-4">
                                                    <div class="form-group required">
                                                        <label id="user_id" class="control-label"> 業務名稱 [TMP] </label>
                                                        <select class="selectpicker validate" name="user_id" data-focusid="user_id" data-style="form-control" data-size="5" data-live-search="false" data-dropup-auto="false">
                                                            <option value="">請選擇</option>
                                                            @foreach ($salesList as $list)
    <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}" @if (isset($storeVisit['user_id']) && $storeVisit['user_id'] == $list['id']) selected @endif>{{ $list['name'] }}</option>
    @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                                -->
                                    <div class="col-md-4">
                                        <div class="form-group required">
                                            <label class="control-label"> 拜訪日期 </label>
                                            <input id="visist_dt" type="text" name="visit_dt" class="form-control"
                                                   placeholder=""
                                                   value="{{ isset($storeVisit['visit_dt']) ? $storeVisit['visit_dt'] : date('Y-m-d') }}"
                                                   autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                                <!--
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group required">
                                                        <label id="sales_supv" class="control-label"> 主管 [TMP] </label>
                                                        <select class="selectpicker" name="sales_supv_id" data-focusid="sales_supv" data-style="form-control" data-size="5" data-live-search="false" data-dropup-auto="false">
                                                            <option value="">請選擇</option>
                                                            @foreach ($salesList as $list)
    <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}" @if (isset($store['sales_supv_id']) && $store['sales_supv_id'] == $list['id']) selected @endif>{{ $list['name'] }}</option>
    @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group required">
                                                        <label id="sales_devt" class="control-label"> 開發 [TMP] </label>
                                                        <select class="selectpicker" name="sales_devt_id" data-focusid="sales_devt" data-style="form-control" data-size="5" data-live-search="false" data-dropup-auto="false">
                                                            <option value="">請選擇</option>
                                                            @foreach ($salesList as $list)
    <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}" @if (isset($store['sales_devt_id']) && $store['sales_devt_id'] == $list['id']) selected @endif>{{ $list['name'] }}</option>
    @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group required">
                                                        <label id="sales_mntc" class="control-label"> 維護 [TMP] </label>
                                                        <select class="selectpicker" name="sales_mntc_id" data-focusid="sales_mntc" data-style="form-control" data-size="5" data-live-search="false" data-dropup-auto="false">
                                                            <option value="">請選擇</option>
                                                            @foreach ($salesList as $list)
    <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}" @if (isset($store['sales_mntc_id']) && $store['sales_mntc_id'] == $list['id']) selected @endif>{{ $list['name'] }}</option>
    @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group required">
                                            <label class="control-label"> 店家名稱 </label>
                                            @if (!isset($storeVisit['store_id']))
                                                <div class="input-group">
                                                    <input type="text" name="name" class="form-control"
                                                           placeholder="">
                                                    <span class="input-group-btn">
                                                        <button type="button" id="import-data"
                                                                class="btn waves-effect waves-light btn-info">匯入</button>
                                                        <button type="button" id="clean-data"
                                                                class="btn waves-effect waves-light btn-warning"
                                                                style="display:none;">清除</button>
                                                    </span>
                                                </div>
                                            @else
                                                <input type="text" name="name" class="form-control"
                                                       value="{{ isset($storeVisit['store_id']) ? $store['name'] : '' }}"
                                                       placeholder="" readonly>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    {{-- <div class="col-md-4"> --}}
                                    {{-- <div class="form-group required"> --}}
                                    {{-- <label id="storearea_id" class="control-label"> 商圈名稱 </label> --}}
                                    {{-- <select class="selectpicker validate" name="storearea_id" data-focusid="storearea_id" data-style="form-control" data-size="5" data-live-search="false" data-dropup-auto="false"> --}}
                                    {{-- <option value="">請選擇</option> --}}
                                    {{-- @foreach ($storeareaList as $list) --}}
                                    {{-- <option data-token="{{$list['name']}}" value="{{$list['id']}}" @if (isset($store['storearea_id']) && $store['storearea_id'] == $list['id']) selected @endif>{{$list['name']}}</option> --}}
                                    {{-- @endforeach --}}
                                    {{-- </select> --}}
                                    {{-- </div> --}}
                                    {{-- </div> --}}
                                    <div class="col-md-4">
                                        <div class="form-group required">
                                            <label id="storecategory_id" class="control-label"> 店家類型 </label>
                                            <i class="ti-agenda" data-toggle="tooltip" data-html="true"
                                               title="商店: 歸類為店家<br>廠商: 非店家都歸類為廠商"></i>
                                            <select class="selectpicker validate" name="store_category_id"
                                                    data-focusid="storecategory_id" data-style="form-control"
                                                    data-size="5"
                                                    data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @foreach ($storecategoryList as $list)
                                                    <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}"
                                                            @if (isset($store['store_category_id']) && $store['store_category_id'] == $list['id']) selected @endif>
                                                        {{ $list['name'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group required">
                                            <label id="storetype_id" class="control-label"> 商圈類型 </label>
                                            <select class="selectpicker validate" name="storetype_id"
                                                    data-focusid="storetype_id" data-style="form-control" data-size="5"
                                                    data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @foreach ($storetypeList as $list)
                                                    <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}"
                                                            @if (isset($store['storetype_id']) && $store['storetype_id'] == $list['id']) selected @endif>
                                                        {{ $list['name'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group required">
                                            <label id="is_open" class="control-label"> 營業狀況 </label>
                                            <select class="selectpicker validate" name="is_open" data-focusid="is_open"
                                                    data-style="form-control" data-size="5" data-live-search="false"
                                                    data-dropup-auto="false">
                                                <option value="1"
                                                        @if (isset($is_open) && $is_open == 1) selected @endif>營業
                                                </option>
                                                <option value="0"
                                                        @if (isset($is_open) && $is_open == 0) selected @endif>歇業
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group required">
                                            <label id="city_id" class="control-label"> 縣市 </label>
                                            <select class="selectpicker validate" name="city_id" data-focusid="city_id"
                                                    data-style="form-control" data-size="5" data-live-search="true"
                                                    data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @foreach ($cityList as $list)
                                                    <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}"
                                                            @if (isset($store['city_id']) && $store['city_id'] == $list['id']) selected @endif>
                                                        {{ $list['name'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group required">
                                            <label id="district_id" class="control-label"> 區域 </label>
                                            <select class="selectpicker validate" name="district_id"
                                                    data-focusid="district_id" data-style="form-control" data-size="5"
                                                    data-live-search="true" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @if (isset($store['district_id']))
                                                    @foreach ($districtList as $list)
                                                        @if ($list['city_id'] == $store['city_id'])
                                                            <option data-token="{{ $list['name'] }}"
                                                                    value="{{ $list['id'] }}"
                                                                    @if (isset($store['district_id']) && $store['district_id'] == $list['id']) selected @endif>
                                                                {{ $list['name'] }}</option>
                                                        @endif
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group required">
                                            <label class="control-label"> 地址 </label>
                                            <input type="text" name="addr" class="form-control"
                                                   value="{{ isset($store['addr']) ? $store['addr'] : '' }}"
                                                   placeholder="">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-8 p-0">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="control-label"> 職稱 </label>
                                                <input type="text" name="position_name" class="form-control"
                                                       value="{{ isset($store['position_name']) ? $store['position_name'] : '' }}"
                                                       placeholder="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group required">
                                                <label class="control-label"> 聯絡人姓名 </label>
                                                <input type="text" name="contactname" class="form-control"
                                                       value="{{ isset($store['contactname']) ? $store['contactname'] : '' }}"
                                                       placeholder="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group required">
                                                <label class="control-label"> 店家電話 </label>
                                                <i class="ti-agenda" data-toggle="tooltip" data-html="true"
                                                   title="分機輸入: phone#0000[#後限5碼]"></i>
                                                <input type="text" name="tel" class="form-control"
                                                       value="{{ isset($store['tel']) ? $store['tel'] : '' }}"
                                                       placeholder="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="control-label"> 語音合作電話 </label>
                                                <input type="text" class="form-control" name="voice_tel"
                                                       value="{{ isset($store['voice_tel']) ? $store['voice_tel'] : '' }}"
                                                       @if (!isset($store['store_cooperation']['lightbox_cooperation_contract_path']) || empty($store['store_cooperation']['lightbox_cooperation_contract_path'])) disabled @endif>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 m-b-20">
                                        <div class="form-group required show-store-open-time-div">
                                            <label class="control-label"> 營業時間 </label>
                                            <div class="input-group">
                                                <div class="form-control show-store-open-time"></div>
                                                <span class="input-group-addon btn btn-info edit-store-open-time"><i
                                                        class="ti-pencil-alt"></i></span>
                                            </div>
                                            <input type="test" style="position: absolute;height: 0;width:0;border:0;"
                                                   name="open_time" id="open_time"
                                                   value="{{ isset($store['open_time']) && $store['open_time'] ? $store['open_time'] : '[]' }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label"> 燈箱合作日期 </label>
                                            <input type="text" class="form-control" name="lightbox_cooperation_date"
                                                   value="{{ isset($store['store_cooperation']['lightbox_cooperation_date']) && $store['store_cooperation']['lightbox_cooperation_date'] ? date('Y-m-d', strtotime($store['store_cooperation']['lightbox_cooperation_date'])) : '' }}"
                                                   @if (!isset($store['store_cooperation']['lightbox_cooperation_contract_path']) || empty($store['store_cooperation']['lightbox_cooperation_contract_path'])) disabled @endif>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group required">
                                            <label class="control-label" id="receive_rebate_mode">
                                                是否發放回饋金 </label>
                                            <select class="selectpicker validate" name="receive_rebate_mode"
                                                    data-focusid="receive_rebate_mode" data-style="form-control"
                                                    data-size="5" data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                <option value="0">否，販售優惠券</option>
                                                <option value="1">否，不需回饋金</option>
                                                <option value="2">是</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label"> 燈箱合作費用 </label>
                                            <input type="text" class="form-control" name="lightbox_cooperation_fee"
                                                   value="{{ isset($store['store_cooperation']) ? $store['store_cooperation']['lightbox_cooperation_fee'] : '' }}">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label id="lightbox_cooperation_type" class="control-label">
                                                燈箱類別 </label>
                                            <select class="selectpicker validate" name="lightbox_cooperation_type"
                                                    data-focusid="lightbox_cooperation_type" data-style="form-control"
                                                    data-size="5" data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                <option value="1"
                                                        @if (isset($store['store_cooperation']) && $store['store_cooperation']['lightbox_cooperation_type'] == 1) selected @endif>
                                                    燈箱 30CM
                                                </option>
                                                <option value="2"
                                                        @if (isset($store['store_cooperation']) && $store['store_cooperation']['lightbox_cooperation_type'] == 2) selected @endif>
                                                    燈箱 40CM
                                                </option>
                                                <option value="3"
                                                        @if (isset($store['store_cooperation']) && $store['store_cooperation']['lightbox_cooperation_type'] == 3) selected @endif>
                                                    燈箱 60CM
                                                </option>
                                                <option value="4"
                                                        @if (isset($store['store_cooperation']) && $store['store_cooperation']['lightbox_cooperation_type'] == 4) selected @endif>
                                                    燈箱 80CM
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <!--<div class="row">
                                                <div class="col-sm-3">
                                                    <div class="form-group">
                                                        <label class="control-label">語音合作</label>
                                                        <div class="radio-list">
                                                            <label class="radio-inline">
                                                                <div class="radio radio-info p-0">
                                                                    <input type="radio" name="is_voice_partner" value="1" @if (isset($store['is_voice_partner']) && $store['is_voice_partner']) checked @endif>
                                                                    <label for="is_online1">是</label>
                                                                </div>
                                                            </label>
                                                            <label class="radio-inline">
                                                                <div class="radio radio-info">
                                                                    <input type="radio" name="is_voice_partner" value="0" @if (!isset($store['is_voice_partner']) || !$store['is_voice_partner']) checked @endif>
                                                                    <label for="is_online2">否</label>
                                                                </div>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>-->
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group required">
                                            <label class="control-label"> 配合積極度 </label>
                                            <div class="radio-cont">
                                                <div class="radio-txt"><i class="ti-face-sad"></i></div>
                                                <div class="radio-items">
                                                    <div class="item radio radio-primary">
                                                        <div class="num">1</div>
                                                        <input type="radio" id="radio1" name="compatible_num[]"
                                                               value="1"
                                                               @if (isset($storeVisit['compatible_num']) && $storeVisit['compatible_num'] == 1) checked @endif>
                                                        <label for="radio1"> </label>
                                                    </div>
                                                    <div class="item radio radio-primary">
                                                        <div class="num">2</div>
                                                        <input type="radio" id="radio2" name="compatible_num[]"
                                                               value="2"
                                                               @if (isset($storeVisit['compatible_num']) && $storeVisit['compatible_num'] == 2) checked @endif>
                                                        <label for="radio2"> </label>
                                                    </div>
                                                    <div class="item radio radio-primary">
                                                        <div class="num">3</div>
                                                        <input type="radio" id="radio3" name="compatible_num[]"
                                                               value="3"
                                                               @if (!isset($storeVisit['compatible_num']) || $storeVisit['compatible_num'] == 3) checked @endif>
                                                        <label for="radio3"></label>
                                                    </div>
                                                    <div class="item radio radio-primary">
                                                        <div class="num">4</div>
                                                        <input type="radio" id="radio4" name="compatible_num[]"
                                                               value="4"
                                                               @if (isset($storeVisit['compatible_num']) && $storeVisit['compatible_num'] == 4) checked @endif>
                                                        <label for="radio4"></label>
                                                    </div>
                                                    <div class="item radio radio-primary">
                                                        <div class="num">5</div>
                                                        <input type="radio" id="radio5" name="compatible_num[]"
                                                               value="5"
                                                               @if (isset($storeVisit['compatible_num']) && $storeVisit['compatible_num'] == 5) checked @endif>
                                                        <label for="radio5"></label>
                                                    </div>
                                                </div>

                                                <div class="radio-txt"><i class="ti-face-smile"></i></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label"> 文宣品擺放 </label>
                                            <div class="check-cont">
                                                @php
                                                    $brochure_tool = isset($storeVisit['brochure_tool']) ? json_decode($storeVisit['brochure_tool'], true) : [];
                                                @endphp
                                                <div class="checkbox checkbox-primary">
                                                    <input type="checkbox" id="checkbox1" name="brochure_tool[]"
                                                           value="1" @if (in_array(1, $brochure_tool)) checked @endif>
                                                    <label for="checkbox1"> DM </label>
                                                </div>
                                                <div class="checkbox checkbox-primary">
                                                    <input type="checkbox" id="checkbox2" name="brochure_tool[]"
                                                           value="2" @if (in_array(2, $brochure_tool)) checked @endif>
                                                    <label for="checkbox2"> 卡座 </label>
                                                </div>
                                                <div class="checkbox checkbox-primary">
                                                    <input type="checkbox" id="checkbox3" name="brochure_tool[]"
                                                           value="3" @if (in_array(3, $brochure_tool)) checked @endif>
                                                    <label for="checkbox3"> 特約貼紙 </label>
                                                </div>
                                                <div class="checkbox checkbox-primary">
                                                    <input type="checkbox" id="checkbox4" name="brochure_tool[]"
                                                           value="4" @if (in_array(4, $brochure_tool)) checked @endif>
                                                    <label for="checkbox4"> 牙籤罐 </label>
                                                </div>
                                                <div class="checkbox checkbox-primary">
                                                    <input type="checkbox" id="checkbox5" name="brochure_tool[]"
                                                           value="5" @if (in_array(4, $brochure_tool)) checked @endif>
                                                    <label for="checkbox5"> 酒杯 </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label"> 店家回饋 </label>
                                            <textarea class="form-control" name="store_feeback"
                                                      rows="5">{{ isset($storeVisit['store_feeback']) ? $storeVisit['store_feeback'] : '' }}</textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="control-label"> 備註 </label>
                                            <textarea class="form-control" name="remark"
                                                      rows="5">{{ isset($storeVisit['remark']) ? $storeVisit['remark'] : '' }}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label"> 照片 </label>
                                            <i class="mdi mdi-alert-octagon" data-toggle="tooltip" data-html="true"
                                               title="格式: JPEG, JPG, PNG <br> 大小: 10MB / PER"></i>
                                            <br>
                                            <label class="btn btn-primary m-r-5">
                                                圖片上傳<input type="file" class="visit-file-upload" name="visit_file"
                                                               style="display: none;" data-num="0" data-corp="0"
                                                               accept="image/png, image/jpg, image/jpeg" multiple>
                                            </label>
                                            <input type="file" name="visit_img_file[]" style="display: none;"
                                                   multiple>
                                        </div>
                                        <div class="upload-img-area">
                                            <?php
                                            $images = isset($storeVisit['images']) && $storeVisit['images'] ? json_decode(
                                                $storeVisit['images'],
                                                true
                                            ) : []; ?>
                                            @foreach ($images as $_key => $_path)
                                                <div class="col-md-3 p-r-10 p-l-0">
                                                    <div class="upload-img file-img-key-{{ $_key }}">
                                                        <img src="{{ '/storage' . $_path }}">
                                                        <a class="delete-img-edit"
                                                           data-deleteimgkey="{{ $_key }}"
                                                           data-path="{{ $_path }}">×</a>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <div class="form-actions m-t-10">
                                    <input type="hidden" name="delete_img_edit">
                                    <button id="form-btn" type="submit" class="btn btn-info"><i
                                                class="fa fa-save"></i> 儲存
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('js_plugins')
    <!-- Auto Complete Live Search -->
    <script src="{{ asset('plugins/bower_components/moment/min/moment.min.js') }}"></script>
    <script src="{{ asset('plugins/bower_components/bootstrap-datepicker/bootstrap-datepicker.min.js') }}"></script>
    <script src="{{ asset('plugins/bower_components/bootstrap-datepicker/locales/bootstrap-datepicker.zh-TW.min.js') }}">
    </script>
    <script src="{{ asset('plugins/icheck/icheck.js') }}"></script>
    <script src="{{ asset('js/store.openTimeModal.js') }}"></script>
@endpush

@push('js')
    <script>
        var districtObj = JSON.parse('<?= json_encode($districtList) ?>');

        /* For Form Validation */
        $(document).ready(function () {
            validator = $("#form").validate({
                ignore: "",
                rules: {
                    name: {
                        required: true
                    },
                    visit_dt: {
                        required: true
                    },
                    storetype_id: {
                        pluginRequired: true
                    },
                    store_category_id: {
                        pluginRequired: true
                    },
                    addr: {
                        required: true
                    },
                    is_open: {
                        required: true
                    },
                    open_time: {
                        check_open_time: true
                    },
                    contactname: {
                        required: true
                    },
                    receive_rebate_mode: {
                        required: true
                    },
                    tel: {
                        required: true,
                        regex: '^[0-9]+(#[0-9]{1,5})?$'
                    },
                    city_id: {
                        pluginRequired: true
                    },
                    district_id: {
                        pluginRequired: true
                    },
                    lightbox_cooperation_type: {
                        required: function () {
                            return $("input[name='lightbox_cooperation_date']").val() !== ""
                        }
                    }
                },
                submitHandler: function (form) {
                    $("#form-btn").prop('disabled', true);
                    form.submit();
                }
            });
        });

        $.validator.addMethod("check_open_time", function (value) {
            return value !== '[]' && value !== "[[],[],[],[],[],[],[]]";
        }, "此欄位為必填，請重新輸入。");

        $("input[name='visit_dt']").datepicker({
            autoclose: true,
            language: 'zh-TW',
            format: 'yyyy-mm-dd'
        });

        $("input[name='lightbox_cooperation_date']").datepicker({
            autoclose: true,
            language: 'zh-TW',
            format: 'yyyy-mm-dd'
        });

        $("input[name='lightbox_cooperation_end_date']").datepicker({
            autoclose: true,
            language: 'zh-TW',
            format: 'yyyy-mm-dd'
        });

        @if (!isset($storeVisit['store_id']))
        /* For Input Live Search */
        $("input[name='name']").autocomplete({
            lookup: JSON.parse('<?= addslashes(json_encode($storeList)) ?>'),
            // onSelect: function(suggestion) {
            //     $("#import-data").removeAttr("disabled");
            //     $("input[name='store_id']").val(suggestion.data);
            // }
        });
        @endif

        /* For Input Live Search That If Col Is Null And Clean */
        // $("input[name='name']").keydown(function(){
        //     $("input[name='store_id']").val(0);
        //     $("#import-data").attr("disabled", true);
        // });

        /* For Input Live Search That Import Store Data*/
        $("#import-data").click(function () {
            var store_id = $("input[name='store_id']").val();

            if (store_id) {
                $.ajax({
                    type: "GET",
                    async: false,
                    url: "/formValidate/getData/Store/name/" + $("input[name='name']").val() +
                        "/storeCooperation",
                    success: function (data) {
                        data = JSON.parse(data)
                        if (data.length > 0) {
                            setCol($("input[name='store_id']"), data[0]['id'], false);
                            setCol($("input[name='tel']"), data[0]['tel'], false);
                            setCol($("input[name='addr']"), data[0]['addr'], false);
                            setCol($("input[name='voice_tel']"), data[0]['voice_tel'], false);
                            setCol($("input[name='contactname']"), data[0]['contactname'], false);
                            setCol($("input[name='position_name']"), data[0]['position_name'], false);
                            setCol($("select[name='city_id']"), data[0]['city_id'], false, 'select');
                            setCol($("select[name='district_id']"), data[0]['district_id'], false,
                                'select');
                            setCol($("select[name='storetype_id']"), data[0]['storetype_id'], false,
                                'select');
                            setCol($("select[name='receive_rebate_mode']"), data[0]['receive_rebate_mode'], false, 'select');
                            setCol($("select[name='store_category_id']"), data[0]['store_category_id'],
                                false, 'select');
                            if (data[0]['store_cooperation']) {
                                setCol($("input[name='lightbox_cooperation_fee']"), data[0][
                                    'store_cooperation'
                                    ]['lightbox_cooperation_fee'], false);
                                setCol($("select[name='lightbox_cooperation_type']"), data[0][
                                    'store_cooperation'
                                    ]['lightbox_cooperation_type'], false, 'select');
                                if (data[0]['store_cooperation']['lightbox_cooperation_date']) {
                                    setCol($("input[name='lightbox_cooperation_date']"), moment(data[0][
                                        'store_cooperation'
                                        ]['lightbox_cooperation_date']).format('YYYY-MM-DD'), false);
                                }
                                if (data[0]['store_cooperation']['lightbox_cooperation_end_date']) {
                                    setCol($("input[name='lightbox_cooperation_end_date']"), moment(
                                        data[0]['store_cooperation'][
                                            'lightbox_cooperation_end_date'
                                            ]).format('YYYY-MM-DD'), false);
                                }
                            }
                            /*
                            if (data[0]['is_voice_partner']) {
                                $("input[name='is_voice_partner'][value='1']").attr('checked', 'checked')
                            } else {
                                $("input[name='is_voice_partner'][value='0']").attr('checked', '')
                            }
                            */

                            setCol($("select[name='is_open']"), data[0]['is_open'], false, 'select');
                            setCol($("select[name='sales_supv_id']"), data[0]['sales_supv_id'], false,
                                'select');
                            setCol($("select[name='sales_devt_id']"), data[0]['sales_devt_id'], false,
                                'select');
                            setCol($("select[name='sales_mntc_id']"), data[0]['sales_mntc_id'], false,
                                'select');

                            let openTimeVal = data[0]['open_time']
                            if (openTimeVal) {
                                $("input[name='open_time']").val(openTimeVal)
                                StoreOpenTimeModal.setOpenTimeRecord(JSON.parse(openTimeVal), true)
                            }

                            $("input[name='name']").attr('readonly', true);
                            $("#import-data").hide();
                            $("#clean-data").show();
                        }
                    }
                });
            }
        });

        $("#clean-data").click(function () {
            setCol($("input[name='store_id']"), '', false);
            setCol($("input[name='tel']"), '', false);
            setCol($("input[name='addr']"), '', false);
            setCol($("input[name='voice_tel']"), '', false);
            setCol($("input[name='contactname']"), '', false);
            setCol($("input[name='position_name']"), '', false);
            setCol($("select[name='city_id']"), '', false, 'select');
            setCol($("select[name='district_id']"), '', false, 'select');
            setCol($("select[name='storetype_id']"), '', false, 'select');
            setCol($("select[name='store_category_id']"), '', false, 'select');

            setCol($("input[name='lightbox_cooperation_fee']"), '', false);
            setCol($("input[name='lightbox_cooperation_date']"), '', false);
            setCol($("input[name='lightbox_cooperation_end_date']"), '', false);
            setCol($("select[name='lightbox_cooperation_type']"), '', false, 'select');

            setCol($("select[name='is_open']"), '', false, 'select');
            setCol($("select[name='sales_supv_id']"), '', false, 'select');
            setCol($("select[name='sales_devt_id']"), '', false, 'select');
            setCol($("select[name='sales_mntc_id']"), '', false, 'select');

            //$("input[name='is_voice_partner'][value='0']").attr('checked', '')

            $("input[name='name']").val('');
            $("input[name='store_id']").val(0);
            $("input[name='name']").attr('readonly', false);
            $(".show-store-open-time").empty()
            $("#clean-data").hide();
            $("#import-data").show();
        })

        function setCol(ele, val, isdisabled, action = null) {
            var element = ele.val(val);
            element.attr('disabled', Boolean(isdisabled))
            if (action == 'select') {
                ele.selectpicker('refresh');
            } else {
                validator.resetForm();
            }
        }

        var relationKey = 'city_id',
            mainElement = $("select[name='city_id']"),
            subElement = $("select[name='district_id']");

        mainElement.on('changed.bs.select refreshed.bs.select', function (e) {
            setRelativeSelectTag(mainElement, subElement, districtObj, relationKey);
        });

        if (mainElement.val() && !subElement.val()) {
            setRelativeSelectTag(mainElement, subElement, districtObj, relationKey);
        }

        function setRelativeSelectTag(mainElement, subElement, subObj, relationKey) {
            var val = mainElement.val();
            subElement.empty().append('<option value="">請選擇</option>');
            for (var key in subObj) {
                if (subObj[key][relationKey] == val) {
                    subElement.append(`<option value="${subObj[key]['id']}">${subObj[key]['name']}</option>`);
                }
            }
            subElement.selectpicker("refresh");
        }

        $(".selectpicker.validate").on("changed.bs.select", function () {
            validator.element($(this));
        })

        /* 燈箱日期 如果移除檢查 燈箱類別 */
        $("input[name='lightbox_cooperation_date']").on("keyup", function () {
            validator.element($("select[name='lightbox_cooperation_type']"))
        })

        let uploadFilesCount = {{ count($images) }};
        const uploadFiles = {};
        // 拜訪圖片上傳
        $('.visit-file-upload').change(function () {
            let elemTag = $(this)[0],
                files = elemTag.files,
                totalCount = uploadFilesCount + files.length

            if (totalCount > 6) {
                swal({
                    title: '上傳超過上限',
                    text: '圖片上傳不得超過6張',
                    type: 'error',
                    showCancelButton: false,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "確定",
                });
                return
            }

            for (const file of files) {
                if (file.size > 10000000) {
                    swal({
                        title: '驗證錯誤',
                        text: '圖片上傳大小不能超過10MB',
                        type: 'error',
                        showCancelButton: false,
                        confirmButtonColor: "#DD6B55",
                        confirmButtonText: "確定",
                    });
                    return;
                }
            }
            let dataTransfer = new DataTransfer()
            for (const _file of files) {
                const uuid = _uuid();
                uploadFiles[uuid] = _file
                var loadFile = function (_file, uuid) {
                    $(".upload-img-area").append(
                        $("<div>", {
                            class: 'col-md-3 p-r-10 p-l-0'
                        }).append(
                            $("<div>", {
                                class: `upload-img file-img-uuid-${uuid}`
                            }).append(
                                $("<img>", {
                                    src: URL.createObjectURL(_file)
                                }),
                                $("<a>", {
                                    class: 'delete-img'
                                }).data(`delete-img-uuid`, uuid).text('×')
                            )
                        )
                    )
                };

                uploadFilesCount += 1
                loadFile(_file, uuid)
            }

            for (const _uuid in uploadFiles) {
                const _file = uploadFiles[_uuid];
                dataTransfer.items.add(_file)
            }

            $("input[name='visit_file']").val("");
            $("input[name='visit_img_file[]']")[0].files = dataTransfer.files
        });

        $('body').on('click', '.delete-img', function () {
            const deleteImgUUid = $(this).data('delete-img-uuid');
            $(`.file-img-uuid-${deleteImgUUid}`).remove();
            delete uploadFiles[deleteImgUUid];
            uploadFilesCount -= 1

            let dataTransfer = new DataTransfer()
            for (const _uuid in uploadFiles) {
                const _file = uploadFiles[_uuid];
                dataTransfer.items.add(_file)
            }

            $("input[name='visit_img_file[]']")[0].files = dataTransfer.files
        });

        let deleteImgEdit = [];
        $('body').on('click', '.delete-img-edit', function () {
            const deleteImgKey = $(this).data('deleteimgkey');
            $(`.file-img-key-${deleteImgKey}`).remove();
            uploadFilesCount -= 1;

            deleteImgEdit.push($(this).data('path'));

            $("input[name='delete_img_edit']").val(JSON.stringify(deleteImgEdit))

        });

        function _uuid() {
            function s4() {
                return Math.floor((1 + Math.random()) * 0x10000).toString(16).substring(1);
            }

            return s4() + s4() + '-' + s4() + '-' + s4() + '-' + s4() + '-' + s4() + s4() + s4();
        }


        /* ================== */
        /* storeOpenTimeModal */
        /* ================== */

        StoreOpenTimeModal.displayContent = function (array) {
            let record = array,
                storeOpenDailyText = ['日', '一', '二', '三', '四', '五', '六']

            $('.show-store-open-time').empty()
            for (let i in record) {

                // let dailyUi = $('<ul>').css({'list-style-type': 'none', 'padding': '0px'}).addClass('pull-left')
                // $('<li>').text('星期' + storeOpenDailyText[i]).appendTo(dailyUi)
                //
                // let timeUi = $('<ul>').css({'list-style-type': 'square', 'font-family': 'Arial'}).addClass('pull-right')
                //
                // if (record[i].length) {
                //     for (let j in record[i]) {
                //         let li = $('<li>').text(record[i][j][0] + ' - ' + record[i][j][1])
                //         li.appendTo(timeUi)
                //     }
                // } else {
                //     let li = $('<li>').text('歇業')
                //     li.appendTo(timeUi)
                // }

                let dailyBlock = $('<div>').addClass('col-sm-12')
                $('<span>').text('星期' + storeOpenDailyText[i]).appendTo(dailyBlock)

                let timeUi = $('<ul>').css({
                    'list-style-type': 'square',
                    'font-family': 'Arial',
                    'letter-spacing': '3px'
                }).addClass('pull-right')

                if (record[i].length) {
                    for (let j in record[i]) {
                        let li = $('<li>').text(record[i][j][0] + ' - ' + record[i][j][1])
                        li.appendTo(timeUi)
                    }
                } else {
                    let li = $('<li>').text('休息')
                    li.appendTo(timeUi)
                }

                timeUi.appendTo(dailyBlock)

                let row = $('<div>').addClass('row')
                row.append(dailyBlock)

                $('.show-store-open-time').append(row)
                if (storeOpenDailyText.length > (parseInt(i) + 1)) {
                    $('.show-store-open-time').append($('<hr>').css({
                        'margin-top': '5px',
                        'margin-bottom': '10px'
                    }))
                }
            }
        }

        StoreOpenTimeModal.init({
            clickModalElem: $(".edit-store-open-time"),
            inputForOpenTimeElem: $("input[name='open_time']"),
            openTimeRecord: <?= isset($store['open_time']) && $store['open_time'] ? $store['open_time'] : '[]' ?>
        })
    </script>
@endPush
