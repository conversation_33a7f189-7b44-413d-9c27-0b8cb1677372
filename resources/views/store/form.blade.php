@include('blocks.sweetalt')
@include('blocks.validation')
@include('blocks.daterangepicker')
@extends('layouts.main')

@push('css_plugins')
    <link rel="stylesheet" type="text/css" href="{{ asset('plugins/icheck/skins/all.css') }}" />
    <link rel="stylesheet" type="text/css" href="{{ asset('css/store.openTimeModal.css?v=1') }}" />
    <link rel="stylesheet" type="text/css" href="{{ asset('plugins/bower_components/dropify/dist/css/dropify.min.css') }}" />

@endpush

@push('css')
    <style>
        input[type="datetime-local"].empty::placeholder {
            color: red;
        }

        .modal {
            overflow-y: auto;
        }

        .tooltip-inner {
            text-align: left;
            max-width: 350px;
        }

        table.dataTable tbody td.select-checkbox {
            top: 7px
        }

        table.dataTable tbody tr.selected {
            color: #797979;
            background-color: #e4e7ea;
        }

        .datepicker.dropdown-menu {
            margin-top: 60px;
        }

        .material-switch>input[type="checkbox"] {
            display: none;
        }

        .material-switch>label {
            cursor: pointer;
            height: 0px;
            position: relative;
            width: 40px;
        }

        .material-switch>label::before {
            background: rgb(0, 0, 0);
            box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            content: '';
            height: 16px;
            margin-top: -8px;
            position: absolute;
            opacity: 0.3;
            transition: all 0.4s ease-in-out;
            width: 40px;
        }

        .material-switch>label::after {
            background: rgb(255, 255, 255);
            border-radius: 16px;
            box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
            content: '';
            height: 24px;
            left: -4px;
            margin-top: -8px;
            position: absolute;
            top: -4px;
            transition: all 0.3s ease-in-out;
            width: 24px;
        }

        .material-switch>input[type="checkbox"]:checked+label::before {
            background: inherit;
            opacity: 0.5;
        }

        .material-switch>input[type="checkbox"]:checked+label::after {
            background: inherit;
            left: 20px;
        }

        .btn-default {
            background-color: #bbb;
            color: #fff;
        }

        .btn-origin {
            color: #fff;
            background-color: rgba(255, 89, 35, 1);
            border-color: rgba(255, 89, 35, 1);
        }

        .btn-origin:hover,
        .btn-origin:focus {
            color: #fff;
        }

        .upload-text {
            margin-right: 15px;
        }

        .upload-text .title {
            font-size: 15px;
            font-weight: 800;
            color: rgba(255, 89, 35, 1);
        }
    </style>
@endpush

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-info">
                <div class="panel-heading">
                    @if (!isset($id))
                        新增特約店家
                    @else
                        修改特約店家
                    @endif
                </div>
                <div class="panel-wrapper collapse in" aria-expanded="true">
                    <div class="panel-body">
                        <form id="form" action="/store/form" method="POST" enctype="multipart/form-data">
                            @csrf
                            <input type="hidden" id="id" name="id" value="{{ isset($id) ? $id : 0 }}">
                            <div class="form-body">
                                @if (($joyMap['is_pay'] ?? 0) || ($joyMap['is_booking'] ?? 0))
                                    <button type="button" id="upload-joymap-btn" class="btn btn-origin pull-right hide"
                                        disabled>上傳Joymap
                                    </button>
                                    <div class="upload-text upload-error pull-right hide">
                                        <span class="title">Joymap上傳失敗</span><br>
                                        <span class="time"></span>
                                    </div>
                                    <div class="upload-text upload-success pull-right hide">
                                        <span class="title">Joymap上傳完成</span><br>
                                        <span class="time"></span>
                                    </div>
                                @endif
                                <h2 class="box-title">店鋪開發資料</h2>
                                <hr>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label id="sales_supv" class="control-label"> 主管 </label>
                                            <select disabled class="selectpicker validate" name="sales_supv_id"
                                                data-focusid="sales_supv" data-style="form-control" data-size="5"
                                                ata-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @foreach ($salesStaffList as $list)
                                                    @if (
                                                        $list['is_supervisor'] == 1 ||
                                                            (isset($sales_supv_id) && $list['id'] == $sales_supv_id) ||
                                                            (isset($manager) && in_array($list['id'], $manager)))
                                                        <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}"
                                                            @if ((isset($sales_supv_id) && $sales_supv_id == $list['id']) || (isset($manager) && in_array($list['id'], $manager))) selected @endif>
                                                            {{ $list['name'] }}</option>
                                                    @endif
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label id="sales_devt" class="control-label"> 開發 </label>
                                            <select disabled class="selectpicker validate" name="sales_devt_id"
                                                data-focusid="sales_devt" data-style="form-control" data-size="5"
                                                data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @if (isset($sales_devt_id))
                                                    @foreach ($salesStaffList as $list)
                                                        @if (
                                                            $list['supervisor_id'] == $sales_supv_id ||
                                                                $list['id'] == $sales_devt_id ||
                                                                (isset($sales_supv_id) && $list['id'] == $sales_supv_id))
                                                            <option data-token="{{ $list['name'] }}"
                                                                value="{{ $list['id'] }}"
                                                                @if (isset($sales_devt_id) && $sales_devt_id == $list['id']) selected @endif>
                                                                {{ $list['name'] }}</option>
                                                        @endif
                                                    @endforeach
                                                @else
                                                    @foreach ($salesStaffList as $list)
                                                        @if ($list['id'] == Auth::id())
                                                            <option data-token="{{ $list['name'] }}"
                                                                value="{{ $list['id'] }}" selected>{{ $list['name'] }}
                                                            </option>
                                                        @endif
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label id="sales_mntc" class="control-label"> 維護 </label>
                                            <select disabled class="selectpicker validate" name="sales_mntc_id"
                                                data-focusid="sales_mntc" data-style="form-control" data-size="5"
                                                data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @if (isset($sales_mntc_id))
                                                    @foreach ($salesStaffList as $list)
                                                        @if (
                                                            $list['supervisor_id'] == $sales_supv_id ||
                                                                $list['id'] == $sales_mntc_id ||
                                                                (isset($sales_supv_id) && $list['id'] == $sales_supv_id))
                                                            <option data-token="{{ $list['name'] }}"
                                                                value="{{ $list['id'] }}"
                                                                @if (isset($sales_mntc_id) && $sales_mntc_id == $list['id']) selected @endif>
                                                                {{ $list['name'] }}</option>
                                                        @endif
                                                    @endforeach
                                                @else
                                                    @foreach ($salesStaffList as $list)
                                                        @if ($list['id'] == Auth::id())
                                                            <option data-token="{{ $list['name'] }}"
                                                                value="{{ $list['id'] }}" selected>{{ $list['name'] }}
                                                            </option>
                                                        @endif
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <h2 class="box-title m-t-40">特約店家資料</h2>
                                <hr>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label class="control-label"> 店家名稱 </label>
                                            <input type="text" class="form-control" placeholder="" autocomplete="off"
                                                name="name" value="{{ isset($name) ? $name : '' }}">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 營業登記名稱 </label>
                                            <input type="text" class="form-control" placeholder="" autocomplete="off"
                                                   name="trade_name" value="{{ $trade_name ?? '' }}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 統一編號 </label>
                                            <input type="number" class="form-control" placeholder="" autocomplete="off"
                                                   name="gui_number" value="{{ $gui_number ?? '' }}" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> PG 推廣 </label>
                                            <div class="checkbox checkbox-primary">
                                                <input type="checkbox" id="checkbox-is-online" name="is_pg_promote"
                                                       value="1"
                                                       @if (isset($is_pg_promote) && $is_pg_promote == 1) checked="checked" @endif>
                                                <label for="checkbox-is-online"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label id="storecategory_id" class="control-label"> 店家類型 </label>
                                            <i class="ti-agenda" data-toggle="tooltip" data-html="true"
                                                title="商店: 歸類為店家<br>廠商: 非店家都歸類為廠商"></i>
                                            <select class="selectpicker validate" name="store_category_id"
                                                data-focusid="storecategory_id" data-style="form-control" data-size="5"
                                                data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @foreach ($storecategoryList as $list)
                                                    <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}"
                                                        @if (isset($store_category_id) && $store_category_id == $list['id']) selected @endif>
                                                        {{ $list['name'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label id="storetype_id" class="control-label"> 商圈類型 </label>
                                            <select class="selectpicker validate" name="storetype_id"
                                                data-focusid="storetype_id" data-style="form-control" data-size="5"
                                                data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @foreach ($storetypeList as $list)
                                                    <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}"
                                                        @if (isset($storetype_id) && $storetype_id == $list['id']) selected @endif>
                                                        {{ $list['name'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label id="store_level_id" class="control-label"> 店家等級 </label>
                                            <i class="ti-agenda" data-toggle="tooltip" data-html="true"
                                                title="
                                            A：有燈箱+主幹道+三角窗+十字路口＋社群粉絲數達7,000以上，或Google Maps達2,000評論以上 <br>
                                            B：有燈箱+主幹道+三角窗+十字路口，或社群粉絲數達7,000以上，或Google Maps達2,000評論以上 <br>
                                            C：有燈箱 <br>
                                            D：純會員
                                        "></i>
                                            <select class="selectpicker validate" name="store_level"
                                                data-focusid="store_level_id" data-style="form-control" data-size="5"
                                                data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                <option value="A" @if (isset($store_level) && $store_level == 'A') selected @endif>
                                                    A
                                                </option>
                                                <option value="B" @if (isset($store_level) && $store_level == 'B') selected @endif>
                                                    B
                                                </option>
                                                <option value="C" @if (isset($store_level) && $store_level == 'C') selected @endif>
                                                    C
                                                </option>
                                                <option value="D" @if (isset($store_level) && $store_level == 'D') selected @endif>
                                                    D
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label id="is_open" class="control-label"> 營業狀況 </label>
                                            <select class="selectpicker validate" name="is_open" data-focusid="is_open"
                                                data-style="form-control" data-size="5" data-live-search="false"
                                                data-dropup-auto="false">
                                                <option value="1" @if (isset($is_open) && $is_open == 1) selected @endif>
                                                    營業
                                                </option>
                                                <option value="0" @if (isset($is_open) && $is_open == 0) selected @endif>
                                                    歇業
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label id="city_id" class="control-label"> 縣市 </label>
                                            <select class="selectpicker validate" name="city_id" data-focusid="city_id"
                                                data-style="form-control" data-size="5" data-live-search="true"
                                                data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @foreach ($cityList as $list)
                                                    <option data-token="{{ $list['name'] }}" value="{{ $list['id'] }}"
                                                        @if (isset($city_id) && $city_id == $list['id']) selected @endif>
                                                        {{ $list['name'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label id="district_id" class="control-label"> 區域 </label>
                                            <select class="selectpicker validate" name="district_id"
                                                data-focusid="district_id" data-style="form-control" data-size="5"
                                                data-live-search="true" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                @if (isset($district_id))
                                                    @foreach ($districtList as $list)
                                                        @if ($list['city_id'] == $city_id)
                                                            <option data-token="{{ $list['name'] }}"
                                                                value="{{ $list['id'] }}"
                                                                @if (isset($district_id) && $district_id == $list['id']) selected @endif>
                                                                {{ $list['name'] }}</option>
                                                        @endif
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group required">
                                            <label class="control-label"> 地址 </label>
                                            <input type="text" class="form-control" placeholder="" name="addr"
                                                value="{{ isset($addr) ? $addr : '' }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 p-0">
                                        <div class="col-md-6">
                                            <div class="form-group required">
                                                <label class="control-label"> 店家電話 </label>
                                                <i class="ti-agenda" data-toggle="tooltip" data-html="true"
                                                    title="分機輸入: phone#0000[#後限5碼]"></i>
                                                <input type="text" class="form-control" placeholder="" name="tel"
                                                    value="{{ isset($tel) ? $tel : '' }}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="control-label"> 職稱 </label>
                                                <input type="text" class="form-control" placeholder=""
                                                    name="position_name"
                                                    value="{{ isset($position_name) ? $position_name : '' }}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group required">
                                                <label class="control-label"> 聯絡人姓名 </label>
                                                <input type="text" class="form-control" placeholder=""
                                                    name="contactname"
                                                    value="{{ isset($contactname) ? $contactname : '' }}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="control-label"> 聯絡人手機 </label>
                                                <input type="text" class="form-control" placeholder=""
                                                    name="contact_phone"
                                                    value="{{ isset($contact_phone) ? $contact_phone : '' }}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 p-0">
                                        <div class="col-md-12">
                                            <div class="form-group required show-store-open-time-div">
                                                <label class="control-label"> 營業時間 </label>
                                                <div class="input-group">
                                                    <div class="form-control show-store-open-time"></div>
                                                    <span class="input-group-addon btn btn-info edit-store-open-time"><i
                                                            class="ti-pencil-alt"></i></span>
                                                </div>
                                                <input type="text"
                                                    style="position:absolute; height: 0;width:0;border:0;" id="open_time"
                                                    name="open_time"
                                                    value="{{ isset($open_time) && $open_time ? $open_time : '[]' }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--<div class="row">
                                                                        <div class="col-sm-3">
                                                                            <div class="form-group">
                                                                                <label class="control-label">語音合作</label>
                                                                                <div class="radio-list">
                                                                                    <label class="radio-inline">
                                                                                        <div class="radio radio-info p-0">
                                                                                            <input type="radio" name="is_voice_partner" value="1" @if (isset($is_voice_partner) && $is_voice_partner) checked @endif>
                                                                                            <label for="is_online1">是</label>
                                                                                        </div>
                                                                                    </label>
                                                                                    <label class="radio-inline">
                                                                                        <div class="radio radio-info">
                                                                                            <input type="radio" name="is_voice_partner" value="0" @if (!isset($is_voice_partner) || !$is_voice_partner) checked @endif>
                                                                                            <label for="is_online2">否</label>
                                                                                        </div>
                                                                                    </label>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>-->
                                {{-- 修改頁面 才顯示銀行帳號區塊 --}}
                                @if (isset($id))
                                    <h2>銀行帳號</h2>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-3 m-b-15">
                                            <label for="input-file-now-custom">存褶封面(檔案限制：JPEG、JPG、PNG，10MB內)</label>
                                            <input type="file" name="bank_pic" class="dropify" data-allowed-file-extensions='["png","jpg"]' data-default-file="{{ $bank_pic ?? '' }}" disabled/>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label class="control-label">銀行名稱</label>
                                                <select name="bank_id" autocomplete="off" class="form-control btn btn-outline" disabled>
                                                    @foreach ($banks as $bank)
                                                    <option value="">請選擇</option>
                                                    <option value="{{ $bank['id'] }}" @if (!empty($bank_id) && $bank['id'] == $bank_id) selected @endif>{{ $bank['name'] }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label class="control-label">分行名稱</label>
                                                <input type="text" class="form-control" name="bankExt" value="{{ $bankExt ?? '' }}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label class="control-label">分行代碼</label>
                                                <input type="number" class="form-control" name="bank_ext_no" value="{{ $bank_ext_no ?? '' }}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label class="control-label">銀行帳號</label>
                                                <input type="number" class="form-control" name="bankAccount" value="{{ $bankAccount ?? '' }}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label class="control-label">銀行戶名</label>
                                                <input type="text" class="form-control" name="bankName" value="{{ $bankName ?? '' }}" readonly>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                                <h2 class="box-title m-t-40">店家合作資料</h2>
                                <hr>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 語音合作電話 </label>
                                            <i class="ti-agenda" data-toggle="tooltip" data-html="true"
                                                title="開通方式: 設定語音電話後透過業務語音開通"></i>
                                            <input type="text" class="form-control" name="voice_tel"
                                                value="{{ isset($voice_tel) ? $voice_tel : '' }}"
                                                @if (!isset($store_cooperation['lightbox_cooperation_contract_path']) || empty($store_cooperation['lightbox_cooperation_contract_path'])) disabled @endif>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 競業合作店家 </label>
                                            <select class="selectpicker" name="is_competition" data-style="form-control"
                                                data-size="5" data-live-search="false" data-dropup-auto="false">
                                                <option value="0" @if (isset($store_cooperation['is_competition']) && $store_cooperation['is_competition'] == 0) selected @endif>
                                                    否
                                                </option>
                                                <option value="1" @if (isset($store_cooperation['is_competition']) && $store_cooperation['is_competition'] == 1) selected @endif>
                                                    是
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group required">
                                            <label class="control-label" id="receive_rebate_mode">
                                                是否發放回饋金 </label>
                                            <select class="selectpicker validate" name="receive_rebate_mode"
                                                data-focusid="receive_rebate_mode" data-style="form-control"
                                                data-size="5" data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                <option value="0" {{ isset($receive_rebate_mode) && $receive_rebate_mode == 0 ? 'selected' : '' }}>
                                                    否，販售優惠券
                                                </option>
                                                <option value="1" {{ isset($receive_rebate_mode) && $receive_rebate_mode == 1 ? 'selected' : '' }}>
                                                    否，不需回饋金
                                                </option>
                                                <option value="2" {{ isset($receive_rebate_mode) && $receive_rebate_mode == 2 ? 'selected' : '' }}>
                                                    是
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 燈箱合作日期 </label>
                                            <input type="text" class="form-control" name="lightbox_cooperation_date"
                                                value="{{ isset($store_cooperation['lightbox_cooperation_date']) ? date('Y-m-d', strtotime($store_cooperation['lightbox_cooperation_date'])) : '' }}"
                                                @if (!isset($store_cooperation['lightbox_cooperation_contract_path']) || empty($store_cooperation['lightbox_cooperation_contract_path'])) disabled @endif>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 燈箱結束合作日期 </label>
                                            @can('store.cooperation.cooperation_end_date', $routeperms)
                                                {{-- <input type="text" class="form-control" name="lightbox_cooperation_end_date" value="{{ isset($store_cooperation['lightbox_cooperation_end_date']) ? date('Y-m-d', strtotime($store_cooperation['lightbox_cooperation_end_date'])) : '' }}"> --}}
                                                <input type="text" class="form-control"
                                                    name="lightbox_cooperation_end_date_disable"
                                                    value="{{ isset($store_cooperation['lightbox_cooperation_end_date']) ? date('Y-m-d', strtotime($store_cooperation['lightbox_cooperation_end_date'])) : '' }}"
                                                    disabled>
                                            @else
                                                <input type="text" class="form-control"
                                                    name="lightbox_cooperation_end_date_disable"
                                                    value="{{ isset($store_cooperation['lightbox_cooperation_end_date']) ? date('Y-m-d', strtotime($store_cooperation['lightbox_cooperation_end_date'])) : '' }}"
                                                    disabled>
                                                <input type="hidden" class="form-control"
                                                    name="lightbox_cooperation_end_date"
                                                    value="{{ isset($store_cooperation['lightbox_cooperation_end_date']) ? date('Y-m-d', strtotime($store_cooperation['lightbox_cooperation_end_date'])) : '' }}">
                                            @endcan
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 燈箱合作同意書 </label>
                                            <br>
                                            @can('store.cooperation.upload', $routeperms)
                                                {{-- <label class="btn btn-primary m-t-5 m-r-5">
                                                    合約上傳<input type="file" class="cooperation-file-upload"
                                                        name="cooperation_file" style="display: none;" data-num="0"
                                                        data-corp="0" accept=".pdf">
                                                </label> --}}
                                                <a href="javascript:void(0)" class="btn btn-default m-r-5"
                                                    disabled="">合約上傳</a>
                                            @endcan
                                            @if (isset($store_cooperation['lightbox_cooperation_contract_path']) &&
                                                    $store_cooperation['lightbox_cooperation_contract_path']
                                            )
                                                <a class="btn btn-info m-t-5 m-r-5"
                                                    href="{{ route('store/cooperation.file.dw', ['store_id' => $id]) }}"
                                                    target="_blank">
                                                    合約下載
                                                </a>
                                                <a class="btn btn-warning m-t-5 m-r-5"
                                                    href="{{ route('store/cooperation.file', ['store_id' => $id]) }}"
                                                    target="_blank">
                                                    合約查看
                                                </a>
                                            @elseif (isset($id))
                                                @cannot('store.cooperation.upload', $routeperms)
                                                    <a href="javascript:void(0)" class="btn btn-default m-r-5"
                                                        disabled="">合約未上傳</a>
                                                @endcannot
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 燈箱合作費用 </label>
                                            <input type="text" class="form-control" name="lightbox_cooperation_fee"
                                                value="{{ isset($store_cooperation['lightbox_cooperation_fee']) ? $store_cooperation['lightbox_cooperation_fee'] : '' }}">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label id="lightbox_cooperation_level_id" class="control-label">
                                                燈箱等級 </label>
                                            <i class="ti-agenda" data-toggle="tooltip" data-html="true"
                                                title="
                                            A：主幹道路邊、三角窗、十字路口、兩條以上道路交叉口、社群(Facebook, Instagram......)粉絲數達7,000以上、Google Maps達2,000評論以上 (任一滿足即可)<br>
                                            B：都不是
                                        "></i>
                                            <select class="selectpicker validate" name="lightbox_cooperation_level"
                                                data-focusid="lightbox_cooperation_level_id" data-style="form-control"
                                                data-size="5" data-live-search="false" data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                <option value="A" @if (isset($store_cooperation['lightbox_cooperation_level']) && $store_cooperation['lightbox_cooperation_level'] == 'A') selected @endif>
                                                    A
                                                </option>
                                                <option value="B" @if (isset($store_cooperation['lightbox_cooperation_level']) && $store_cooperation['lightbox_cooperation_level'] == 'B') selected @endif>
                                                    B
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 燈箱尺寸 </label>
                                            <select class="selectpicker validate" name="lightbox_cooperation_type"
                                                data-style="form-control" data-size="5" data-live-search="false"
                                                data-dropup-auto="false">
                                                <option value="">請選擇</option>
                                                <option value="1" @if (isset($store_cooperation['lightbox_cooperation_type']) && $store_cooperation['lightbox_cooperation_type'] == 1) selected @endif>
                                                    燈箱 30CM
                                                </option>
                                                <option value="2" @if (isset($store_cooperation['lightbox_cooperation_type']) && $store_cooperation['lightbox_cooperation_type'] == 2) selected @endif>
                                                    燈箱 40CM
                                                </option>
                                                <option value="3" @if (isset($store_cooperation['lightbox_cooperation_type']) && $store_cooperation['lightbox_cooperation_type'] == 3) selected @endif>
                                                    燈箱 60CM
                                                </option>
                                                <option value="4" @if (isset($store_cooperation['lightbox_cooperation_type']) && $store_cooperation['lightbox_cooperation_type'] == 4) selected @endif>
                                                    燈箱 80CM
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> QRcode 優惠券 </label>
                                            <select class="selectpicker" name="web_coupon_is_active"
                                                data-style="form-control" data-size="5" data-live-search="false"
                                                data-dropup-auto="false">
                                                <option value="0" @if (!isset($store_cooperation['web_coupon_is_active']) || $store_cooperation['web_coupon_is_active'] == 0) selected @endif>
                                                    否
                                                </option>
                                                <option value="1" @if (isset($store_cooperation['web_coupon_is_active']) && $store_cooperation['web_coupon_is_active'] == 1) selected @endif>
                                                    是
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 優惠券面額（元） </label>
                                            <select disabled class="selectpicker" name="web_coupon_money"
                                                data-style="form-control" data-size="5" data-live-search="false"
                                                data-dropup-auto="false">
                                                @if (!isset($store_cooperation['web_coupon_money']) || $store_cooperation['web_coupon_money'] == 0)
                                                    <option value="50">50</option>
                                                    <option value="100" selected>100</option>
                                                    <option value="150">150</option>
                                                @else
                                                    <option value="50"
                                                        @if (isset($store_cooperation['web_coupon_money']) && $store_cooperation['web_coupon_money'] == 50) selected @endif>
                                                        50
                                                    </option>
                                                    <option value="100"
                                                        @if (isset($store_cooperation['web_coupon_money']) && $store_cooperation['web_coupon_money'] == 100) selected @endif>
                                                        100
                                                    </option>
                                                    <option value="150"
                                                        @if (isset($store_cooperation['web_coupon_money']) && $store_cooperation['web_coupon_money'] == 150) selected @endif>
                                                        150
                                                    </option>
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 優惠券每月上限（張） </label>
                                            <div class="input-group">
                                                <input disabled type="text" name="web_coupon_num" class="form-control"
                                                    value="{{ isset($store_cooperation['web_coupon_num']) && $store_cooperation['web_coupon_num'] ? $store_cooperation['web_coupon_num'] : 10 }}">
                                                <span class="input-group-btn">
                                                    @if (isset($store_cooperation['web_coupon_used_num']))
                                                        <a type="javascript:void(0)"
                                                            class="btn waves-effect waves-light btn-info">本月使用:{{ $store_cooperation['web_coupon_used_num'] }}
                                                            (張)</a>
                                                    @else
                                                        <a type="javascript:void(0)"
                                                            class="btn waves-effect waves-light btn-info">本月使用: 0 (張)</a>
                                                    @endif
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> App 優惠券 </label>
                                            <select class="selectpicker" name="app_coupon_is_active"
                                                data-style="form-control" data-size="5" data-live-search="false"
                                                data-dropup-auto="false">
                                                <option value="0" @if (!isset($store_cooperation['app_coupon_is_active']) || $store_cooperation['app_coupon_is_active'] == 0) selected @endif>
                                                    否
                                                </option>
                                                <option value="1" @if (isset($store_cooperation['app_coupon_is_active']) && $store_cooperation['app_coupon_is_active'] == 1) selected @endif>
                                                    是
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 優惠券面額（元） </label>
                                            <select disabled class="selectpicker" name="app_coupon_money"
                                                data-style="form-control" data-size="5" data-live-search="false"
                                                data-dropup-auto="false">
                                                @if (!isset($store_cooperation['app_coupon_money']) || $store_cooperation['app_coupon_money'] == 0)
                                                    <option value="50">50</option>
                                                    <option value="100" selected>100</option>
                                                    <option value="150">150</option>
                                                @else
                                                    <option value="50"
                                                        @if (isset($store_cooperation['app_coupon_money']) && $store_cooperation['app_coupon_money'] == 50) selected @endif>
                                                        50
                                                    </option>
                                                    <option value="100"
                                                        @if (isset($store_cooperation['app_coupon_money']) && $store_cooperation['app_coupon_money'] == 100) selected @endif>
                                                        100
                                                    </option>
                                                    <option value="150"
                                                        @if (isset($store_cooperation['app_coupon_money']) && $store_cooperation['app_coupon_money'] == 150) selected @endif>
                                                        150
                                                    </option>
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 優惠券每月上限（張） </label>
                                            <div class="input-group">
                                                <input disabled type="text" name="app_coupon_num" class="form-control"
                                                    value="{{ isset($store_cooperation['app_coupon_num']) && $store_cooperation['app_coupon_num'] ? $store_cooperation['app_coupon_num'] : 10 }}">
                                                <span class="input-group-btn">
                                                    @if (isset($store_cooperation['app_coupon_used_num']))
                                                        <a type="javascript:void(0)"
                                                            class="btn waves-effect waves-light btn-info">本月使用:{{ $store_cooperation['app_coupon_used_num'] }}
                                                            (張)</a>
                                                    @else
                                                        <a type="javascript:void(0)"
                                                            class="btn waves-effect waves-light btn-info">本月使用: 0 (張)</a>
                                                    @endif
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 合作店家 </label>
                                            <select @if (!isset($store_cooperation['lightbox_cooperation_contract_path']) || empty($store_cooperation['lightbox_cooperation_contract_path'])) disabled @endif class="selectpicker validate" name="app_user_id"
                                                data-style="form-control" data-size="5" data-live-search="false"
                                                data-dropup-auto="false">

                                                <option value="0">
                                                    請選擇
                                                </option>
                                                @foreach($store_cooperation_users as $user)
                                                <option value="{{ $user['id'] }}" @if (isset($store_cooperation['app_user_id']) && $store_cooperation['app_user_id'] == $user['id']) selected @endif>
                                                    {{ $user['name'] }}
                                                </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 合作店家開通日 </label>
                                            <input type="text" class="form-control" name="app_active_at"
                                                value="{{ isset($store_cooperation['app_active_at']) ? date('Y-m-d', strtotime($store_cooperation['app_active_at'])) : '' }}"
                                                @if (!isset($store_cooperation['lightbox_cooperation_contract_path']) || empty($store_cooperation['lightbox_cooperation_contract_path'])) disabled @endif>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 店家App帳號 </label>
                                            <input type="text" class="form-control" name="app_account"
                                                value="{{ isset($store_app_account['account']) ? $store_app_account['account'] : '' }}"
                                                autocomplete="off" role="presentation" readonly
                                                onMouseDown="this.removeAttribute('readonly')"
                                                @if (!isset($store_cooperation['lightbox_cooperation_contract_path']) || empty($store_cooperation['lightbox_cooperation_contract_path'])) disabled @endif>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 店家App密碼 </label>
                                            <input type="password" class="form-control" name="app_password"
                                                value="" autocomplete="off" role="presentation" readonly
                                                onMouseDown="this.removeAttribute('readonly')"
                                                @if (!isset($store_cooperation['lightbox_cooperation_contract_path']) || empty($store_cooperation['lightbox_cooperation_contract_path'])) disabled @endif>
                                            <input type="hidden" class="form-control" name="app_account_id"
                                                value="{{ isset($store_app_account['id']) ? $store_app_account['id'] : '' }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 專案加價開始時間（包含店家語音、貼紙、APP呼叫任務）</label>
                                            <input class="form-control date-range" type="text" id="extra_price_start_at"
                                                name="extra_price_start_at"
                                                value="{{ isset($store_extra_price) ? $store_extra_price['start_at'] : null }}"
                                                disabled>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 專案加價結束時間（包含店家語音、貼紙、APP呼叫任務）</label>
                                            <input class="form-control date-range" type="text" id="extra_price_end_at"
                                                name="extra_price_end_at"
                                                value="{{ isset($store_extra_price) ? $store_extra_price['end_at'] : null }}"
                                                disabled>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="control-label"> 專案加價（元）（包含店家語音、貼紙、APP呼叫任務）</label>
                                            <input class="form-control" type="number" id="extra_price"
                                                name="extra_price"
                                                value="{{ isset($store_extra_price) ? $store_extra_price['price'] : null }}"
                                                disabled>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-10">
                                        <input type="hidden" name="cooperation_image" value="null">
                                        <div class="row" id="cooperation_list">
                                            @if (isset($cooperationList))
                                                @foreach ($cooperationList as $list)
                                                    <div
                                                        class="m-t-10 col-md-3 col-sm-6 col-xs-12 text-center cooperation">
                                                        <button type="button" class="close"
                                                            onclick="$(this).parent('.cooperation').remove();">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                        <input type="hidden" name="cooperation_image[]"
                                                            value="{{ $list['url'] }}">
                                                        <a href="{{ $list['url'] }}" target="_blank"><img
                                                                class="img-responsive img-thumbnail"
                                                                src="{{ $list['url'] }}"></a>
                                                    </div>
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                @if (($joyMap['is_pay'] ?? 0) || ($joyMap['is_booking'] ?? 0))
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="row m-t-40">
                                                <div class="col-md-8">
                                                    <h2 class="box-title text-warning" style="display: inline;">
                                                        <b>已選擇加入享樂地圖</b>
                                                    </h2>
                                                    <span class="m-l-5" style="display: inline;">
                                                        {{ $joyMap['created_at'] }}</span>
                                                </div>
                                                <div class="col-md-4 text-right">
                                                    <a href="javascript:;"
                                                        style="display: inline; position: relative; top:8px; right:5px;"
                                                        id="clear-joymap-btn" class="hide"
                                                        onclick="clearJoyMap();">取消加入</a>
                                                    <button type="button" id="joy-map-modal-btn" class="btn btn-warning"
                                                        style="display: inline;">JoyMap資料補充
                                                    </button>
                                                </div>
                                            </div>
                                            <hr>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="m-t-10">
                                                        <table class="table">
                                                            <thead>
                                                                <tr>
                                                                    <th scope="col" width="15%">功能</th>
                                                                    <th scope="col" width="10%">開通狀態</th>
                                                                    <th scope="col" width="750%"></th>
                                                                    {{-- <th scope="col">說明</th> --}}
                                                                    {{-- <th scope="col">狀態</th> --}}
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr>
                                                                    <th scope="row">支付功能</th>
                                                                    <td class="td-joymap">
                                                                        @if ($joyMap['is_pay'] ?? 0)
                                                                            開通
                                                                        @else
                                                                            未開通 <a href="javascript:;"
                                                                                onclick="openJoyMap(1);">開通</a>
                                                                        @endif
                                                                    </td>
                                                                    <td></td>
                                                                    {{-- <td> --}}
                                                                    {{-- @if ($joyMap['is_pay'] ?? 0) --}}
                                                                    {{-- @if ($joyMap['is_pay_check'] ?? 0) <span class="text-purple">支付審核完成</span>  @else <span class="text-danger">支付審核未完成</span> @endif --}}
                                                                    {{-- @if ($joyMap['is_qrcode'] ?? 0) 、<span class="text-purple">QRcode佈置完成</span> @else 、<span class="text-danger">QRcode佈置未完成</span> @endif --}}
                                                                    {{-- @else --}}
                                                                    {{-- - --}}
                                                                    {{-- @endif --}}
                                                                    {{-- </td> --}}
                                                                    {{-- <td> --}}
                                                                    {{-- @if ($joyMap['is_pay'] ?? 0) --}}
                                                                    {{-- @if (($joyMap['is_pay_check'] ?? 0) && ($joyMap['is_qrcode'] ?? 0)) 已啟用 @else 未啟用 @endif --}}
                                                                    {{-- @else --}}
                                                                    {{-- - --}}
                                                                    {{-- @endif --}}
                                                                    {{-- </td> --}}
                                                                </tr>
                                                                <tr>
                                                                    <th scope="row">訂位功能</th>
                                                                    <td class="td-joymap">
                                                                        @if ($joyMap['is_booking'] ?? 0)
                                                                            開通
                                                                        @else
                                                                            未開通 <a href="javascript:;"
                                                                                onclick="openJoyMap(2);">開通</a>
                                                                        @endif
                                                                    </td>
                                                                    <td></td>
                                                                    {{-- <td> --}}
                                                                    {{-- @if ($joyMap['is_booking'] ?? 0) --}}
                                                                    {{-- @if ($joyMap['is_trained'] ?? 0) <span class="text-purple">教育訓練完成</span> @else <span class="text-danger">教育訓練未完成</span> @endif --}}
                                                                    {{-- @else --}}
                                                                    {{-- - --}}
                                                                    {{-- @endif --}}
                                                                    {{-- </td> --}}
                                                                    {{-- <td> --}}
                                                                    {{-- @if ($joyMap['is_booking'] ?? 0) --}}
                                                                    {{-- @if ($joyMap['is_trained'] ?? 0) 已啟用 @else 未啟用 @endif --}}
                                                                    {{-- @else --}}
                                                                    {{-- - --}}
                                                                    {{-- @endif --}}
                                                                    {{-- </td> --}}
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <div class="row">
                                        <hr>
                                        <div class="col-md-6">
                                            <button type="button" onclick="$('#joymap').toggle();"
                                                class="btn btn-origin">加入享樂地圖
                                            </button>
                                            <div id="joymap" class="m-t-10" style="display: none;">
                                                <span>請選擇開通合作項目：最少需開通一項</span>
                                                <hr>
                                                <div class="row">
                                                    <div class="col-xs-6">
                                                        <div class="form-group">
                                                            <label class="control-label"> 支付功能 </label>
                                                            <div class="material-switch ">
                                                                <input name="is_pay" type="hidden" value="0">
                                                                <input id="is_pay" name="is_pay" type="checkbox"
                                                                    value="1">
                                                                <label for="is_pay" class="label-primary"></label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-xs-6">
                                                        <div class="form-group">
                                                            <label class="control-label"> 訂位功能 </label>
                                                            <div class="material-switch ">
                                                                <input name="is_booking" type="hidden" value="0">
                                                                <input id="is_booking" name="is_booking" type="checkbox"
                                                                    value="1">
                                                                <label for="is_booking" class="label-primary"></label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                                <div class="form-actions m-t-40">
                                    <button id="form-btn" type="submit" class="btn btn-info"><i
                                            class="fa fa-save"></i> 儲存
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('store.joymapForm')
@endsection

@push('js_plugins')
    <script src="{{ asset('plugins/bower_components/bootstrap-datepicker/bootstrap-datepicker.min.js') }}"></script>
    <script src="{{ asset('plugins/bower_components/bootstrap-datepicker/locales/bootstrap-datepicker.zh-TW.min.js') }}"></script>
    <script src="{{ asset('plugins/bower_components/dropify/dist/js/dropify.min.js') }}"></script>
    <script src="{{ asset('plugins/icheck/icheck.js') }}"></script>
    <script src="{{ asset('js/store.openTimeModal.js?v=2') }}"></script>
@endpush

@push('js')
    <script>
        $(document).ready(function() {
            $('.date-range').daterangepicker({
                singleDatePicker: true, // 只选择单个日期
                timePicker: true, // 启用时间选择器
                timePicker24Hour: true, // 24小时制
                timePickerSeconds: true, // 显示秒
                autoUpdateInput: false,
                locale: {
                    format: 'YYYY-MM-DD HH:mm:ss', // 设置日期时间格式
                    applyLabel: "確定",
                    cancelLabel: "取消",
                    daysOfWeek: ["日", "一", "二", "三", "四", "五", "六"],
                    monthNames: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                    firstDay: 1
                }
            });

            $('.date-range').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD HH:mm:ss'));
            });

            $('.date-range').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
        });

        var form = $('#form'),
            id = form.find('#id').val()

        $("#joy-map-modal-btn").click(function() {
            joymap.loadData(id)
            $("#joyMapModal").modal('show')
        })

        @if (($joyMap['is_pay'] ?? 0) || ($joyMap['is_booking'] ?? 0))
            joymap.loadData(id)

            $("#upload-joymap-btn").click(function() {
                $(this).prop('disabled', true)
                joymap.upload(id, function() {
                    $(this).prop('disabled', false)
                })
            })
        @endif

        var drEvent = $('.dropify').dropify({
            showRemove: false,
            messages: {
                default: '點選上傳或拖曳照片至此',
                replace: '更換照片',
                remove: '清除',
                error: '上傳錯誤'
            },
            error: {
                fileExtension: 'The file extension is not allowed. Only png and jpg files are allowed.'
            }

        });

        drEvent.on('dropify.errors', function(event, element) {
            alert(element.settings.error.fileExtension);
        });

        function clearJoyMap() {
            let data = new FormData();
            data.append("_token", "{{ csrf_token() }}");
            swal({
                title: '取消後所有Joymap補充資料皆清空，無法復原，請確認是否取消？',
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "確定",
                cancelButtonText: "取消",
                closeOnConfirm: false
            }, function(isConfirm) {
                if (isConfirm) {
                    $.ajax({
                        data: data,
                        type: "POST",
                        url: "/store-joy-map/clear/" + id,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function(data) {

                            if (data.status === 1) {
                                swal({
                                    title: '取消成功',
                                    type: 'success',
                                    timer: 3000,
                                });
                                location.href = location.href;
                            } else {
                                swal({
                                    title: 'Joymap取消加入失敗:' + data.message,
                                    type: 'error',
                                    showCancelButton: false,
                                    confirmButtonColor: "#DD6B55",
                                    confirmButtonText: "確定",
                                });
                            }
                        },
                        error: function() {
                            alert('失敗');
                        }
                    });
                }
            });
        }
    </script>
    <script>
        var districtObj = JSON.parse('<?= json_encode($districtList) ?>');
        var salesStaffObj = JSON.parse('<?= json_encode($salesStaffList) ?>');

        $(document).ready(function() {
            validator = $("#form").validate({
                ignore: "",
                rules: {
                    name: {
                        required: true,
                        formValidateValue: ['store', {{ isset($id) ? $id : 0 }}, 'name']
                    },
                    store_level: {
                        pluginRequired: true
                    },
                    tel: {
                        required: true,
                        regex: '^[0-9]+(#[0-9]{1,5})?$'
                    },
                    contact_phone: {
                        regex: '^09[0-9]{8}$'
                    },
                    addr: {
                        required: true
                    },
                    receive_rebate_mode: {
                        required: true
                    },
                    // storearea_id: {
                    //     pluginRequired: true
                    // },
                    is_open: {
                        required: true
                    },
                    store_category_id: {
                        required: true
                    },
                    storetype_id: {
                        pluginRequired: true
                    },
                    sales_supv_id: {
                        pluginRequired: true
                    },
                    sales_devt_id: {
                        pluginRequired: true
                    },
                    sales_mntc_id: {
                        pluginRequired: true
                    },
                    contactname: {
                        required: true
                    },
                    open_time: {
                        check_open_time: true
                    },
                    city_id: {
                        pluginRequired: true
                    },
                    district_id: {
                        pluginRequired: true
                    },
                    lightbox_cooperation_level: {
                        required: function() {
                            return $("input[name='lightbox_cooperation_date']").val() !== ""
                        }
                    },
                    lightbox_cooperation_type: {
                        required: function() {
                            return $("input[name='lightbox_cooperation_date']").val() !== ""
                        }
                    },
                    web_coupon_num: {
                        min: 0,
                        max: 30,
                        digits: true
                    },
                    app_coupon_num: {
                        min: 0,
                        max: 30,
                        digits: true
                    },
                    app_password: {
                        required: function() {
                            return $("input[name='app_account']").val() !== '' &&
                                $("input[name='app_account_id']").val() === ""
                        }
                    }
                },
                submitHandler: function(form) {
                    $("#form-btn").prop('disabled', true);
                    $('#form').append('<input type="hidden" name="sales_supv_id" value="' + $(
                        'select[name="sales_supv_id"]').val() + '">');
                    $('#form').append('<input type="hidden" name="sales_devt_id" value="' + $(
                        'select[name="sales_devt_id"]').val() + '">');
                    $('#form').append('<input type="hidden" name="sales_mntc_id" value="' + $(
                        'select[name="sales_mntc_id"]').val() + '">');
                    $('#form').append(
                        '<input type="hidden" name="lightbox_cooperation_end_date_disable" value="' +
                        $('input[name="lightbox_cooperation_end_date_disable"]').val() + '">');
                    $('#form').append('<input type="hidden" name="web_coupon_money" value="' + $(
                        'select[name="web_coupon_money"]').val() + '">');
                    $('#form').append('<input type="hidden" name="web_coupon_num" value="' + $(
                        'input[name="web_coupon_num"]').val() + '">');
                    $('#form').append('<input type="hidden" name="app_coupon_money" value="' + $(
                        'select[name="app_coupon_money"]').val() + '">');
                    $('#form').append('<input type="hidden" name="app_coupon_num" value="' + $(
                        'input[name="app_coupon_num"]').val() + '">');
                    form.submit();
                }
            });
        });

        $.validator.addMethod("check_open_time", function(value) {
            return value !== '[]' && value !== "[[],[],[],[],[],[],[]]";
        }, "此欄位為必填，請重新輸入。");

        $("input[name='lightbox_cooperation_date']").datepicker({
            autoclose: true,
            language: 'zh-TW',
            format: 'yyyy-mm-dd'
        });

        $("input[name='lightbox_cooperation_end_date']").datepicker({
            autoclose: true,
            language: 'zh-TW',
            format: 'yyyy-mm-dd'
        });

        jQuery("input[name='app_active_at']").datepicker({
            autoclose: true,
            language: 'zh-TW',
            format: 'yyyy-mm-dd'
        });

        var cityRelationKey = 'city_id',
            cityElement = $("select[name='city_id']"),
            districtElement = $("select[name='district_id']");

        cityElement.on('changed.bs.select refreshed.bs.select', function(e) {
            setRelativeSelectTag(cityElement, districtElement, districtObj, cityRelationKey);
        });

        if (cityElement.val() && !districtElement.val()) {
            setRelativeSelectTag(cityElement, districtElement, districtObj, cityRelationKey);
        }

        function setRelativeSelectTag(mainElement, subElement, subObj, relationKey) {
            var val = mainElement.val();
            subElement.empty().append('<option value="">請選擇</option>');

            for (var key in subObj) {
                if (subObj[key][cityRelationKey] == val) {
                    subElement.append(`<option value="${subObj[key]['id']}">${subObj[key]['name']}</option>`);
                }
            }
            subElement.selectpicker("refresh");
        }

        var relationKey = 'supervisor_id',
            supvElement = $("select[name='sales_supv_id']"),
            devtElement = $("select[name='sales_devt_id']"),
            mntcElement = $("select[name='sales_mntc_id']");

        supvElement.on('changed.bs.select refreshed.bs.select', function(e) {
            setRelativeSelectTagFormSales(supvElement, devtElement, salesStaffObj, relationKey);
            setRelativeSelectTagFormSales(supvElement, mntcElement, salesStaffObj, relationKey);
        });

        if (supvElement.val() && !devtElement.val()) {
            setRelativeSelectTagFormSales(supvElement, devtElement, salesStaffObj, relationKey);
        }

        if (supvElement.val() && !mntcElement.val()) {
            setRelativeSelectTagFormSales(supvElement, mntcElement, salesStaffObj, relationKey);
        }

        function setRelativeSelectTagFormSales(mainElement, subElement, subObj, relationKey) {
            var val = mainElement.val(),
                text = mainElement.find(':selected').text();

            subElement.empty().append('<option value="">請選擇</option>');
            if (val) {
                subElement.append("<option value=" + val + ">" + text + "</option>");
            }
            for (var key in subObj) {
                if (subObj[key][relationKey] == val) {
                    subElement.append(`<option value="${subObj[key]['id']}">${subObj[key]['name']}</option>`);
                }
            }
            subElement.selectpicker("refresh");
        }

        $(".selectpicker.validate").on("changed.bs.select", function() {
            validator.element($(this));
        })

        /* 燈箱日期 如果移除檢查 燈箱類別 */
        $("input[name='lightbox_cooperation_date']").on("keyup", function() {
            validator.element($("select[name='lightbox_cooperation_type']"))
        })

        // 店家合約上傳
        $('.cooperation-file-upload').change(function() {
            let elemTag = $(this)[0],
                file = elemTag.files[0];

            if (file.size / 1024 / 1024 > 1) {
                $(this).val('');
                swal({
                    title: '驗證錯誤',
                    text: '燈箱合作同意書上傳大小不能超過1MB',
                    type: 'error',
                    showCancelButton: false,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "確定",
                });
            }
        });


        /* ================== */
        /* storeOpenTimeModal */
        /* ================== */

        StoreOpenTimeModal.displayContent = function(array) {
            let record = array,
                storeOpenDailyText = ['日', '一', '二', '三', '四', '五', '六']

            $('.show-store-open-time').empty()
            for (let i in record) {

                // let dailyUi = $('<ul>').css({'list-style-type': 'none', 'padding': '0px'}).addClass('pull-left')
                // $('<li>').text('星期' + storeOpenDailyText[i]).appendTo(dailyUi)
                //
                // let timeUi = $('<ul>').css({'list-style-type': 'square', 'font-family': 'Arial'}).addClass('pull-right')
                //
                // if (record[i].length) {
                //     for (let j in record[i]) {
                //         let li = $('<li>').text(record[i][j][0] + ' - ' + record[i][j][1])
                //         li.appendTo(timeUi)
                //     }
                // } else {
                //     let li = $('<li>').text('歇業')
                //     li.appendTo(timeUi)
                // }

                let dailyBlock = $('<div>').addClass('col-sm-12')
                $('<span>').text('星期' + storeOpenDailyText[i]).appendTo(dailyBlock)

                let timeUi = $('<ul>').css({
                    'list-style-type': 'square',
                    'font-family': 'Arial',
                    'letter-spacing': '3px'
                }).addClass('pull-right')

                if (record[i].length) {
                    for (let j in record[i]) {
                        let li = $('<li>').text(record[i][j][0] + ' - ' + record[i][j][1])
                        li.appendTo(timeUi)
                    }
                } else {
                    let li = $('<li>').text('休息')
                    li.appendTo(timeUi)
                }

                timeUi.appendTo(dailyBlock)

                let row = $('<div>').addClass('row')
                row.append(dailyBlock)

                $('.show-store-open-time').append(row)
                if (storeOpenDailyText.length > (parseInt(i) + 1)) {
                    $('.show-store-open-time').append($('<hr>').css({
                        'margin-top': '5px',
                        'margin-bottom': '10px'
                    }))
                }
            }
        }

        StoreOpenTimeModal.init({
            clickModalElem: $(".edit-store-open-time"),
            inputForOpenTimeElem: $("input[name='open_time']"),
            openTimeRecord: <?= isset($open_time) && $open_time ? $open_time : '[]' ?>
        });

        function addCooperationImage(input) {
            if ($('.cooperation').length >= 5) {
                alert('限5張以內');
                return false;
            }
            data = new FormData();
            data.append("image", input.files[0]);
            data.append("_token", "{{ csrf_token() }}");
            $.ajax({
                data: data,
                type: "POST",
                url: "{{ route('store/image.upload') }}",
                cache: false,
                contentType: false,
                processData: false,
                success: function(url) {
                    $('#cooperation_list').append(`
                        <div class="col-md-3 col-sm-6 col-xs-12 text-center cooperation">
                            <button type="button" class="close" onclick="$(this).parent('.cooperation').remove();">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <input type="hidden" name="cooperation_image[]" value="${url}">
                            <a href="${url}" target="_blank"><img class="img-responsive img-thumbnail" src="${url}"></a>
                        </div>
                    `);
                },
                error: function() {
                    alert('上傳失敗');
                }
            });
            $('#ff').val('');
        }

        function openJoyMap(type) {
            let title = '';
            if (type === 1) {
                title = '請確認是否開通“支付功能“';
            } else if (type === 2) {
                title = '請確認是否開通“訂位功能“';
            }
            swal({
                title: title,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "確定",
                cancelButtonText: "取消",
                closeOnConfirm: false
            }, function(isConfirm) {
                if (isConfirm) {
                    data = new FormData();
                    data.append("_token", "{{ csrf_token() }}");
                    $.ajax({
                        data: data,
                        type: "POST",
                        url: "/store/open-joy-map/" + id + "/" + type,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function(data) {
                            if (data.status == 1) {
                                $('.td-joymap').html('開通');
                                swal({
                                    title: '操作成功',
                                    type: 'success',
                                    timer: 3000,
                                });
                            } else {
                                swal({
                                    title: data.message,
                                    type: 'error',
                                    timer: 3000,
                                });
                            }
                        },
                        error: function() {
                            alert('操作失敗');
                        }
                    });
                }
            });
        }

        // 監聽店家App帳號欄位變化
        $("input[name='app_account']").on('input change', function() {
            var appAccount = $(this).val().trim();
            if (appAccount === '') {
                // 如果帳號被清空，清除密碼欄位
                $("input[name='app_password']").val('');
                // 當帳號被清空時，後端會自動刪除 StoreAppAccount 記錄，從而移除「店家App」標籤
            }
        });
    </script>
@endPush
